"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Agent,
  ChatMessage,
  LivekitInputMessageType,
  LivekitNormalOutputMessageType,
  LivekitWorkflowUpdateMessageType,
  WorkflowPayload,
  WorkflowStep,
  MessageList,
} from "@/shared/interfaces";
import { ChatInput } from "./ChatInput";
import { EmployeeChatStarter } from "./EmployeeChatStarter";
import { ChatBubble } from "./ChatBubble";
import { useRoomContext } from "@livekit/components-react";
import { LoaderIcon } from "lucide-react";
import {
  ApprovalDecision,
  LivekitRoomTopics,
  SenderType,
  StepsStatus,
  WorkflowStatus,
} from "@/shared/enums";
import { useUserStore } from "@/hooks/use-user";
import { WorkflowStartingForm } from "./WorkflowStartingForm";
import { sanitizeString } from "@/services/helper";
import { toast } from "sonner";
import { InputChatBubble } from "./InputChatBubble";
import { OutputChatBubble } from "./OutputChatBubble";
import { useParams } from "next/navigation";
import { communicationApi } from "@/app/api/communication";
import { useInfiniteQuery } from "@tanstack/react-query";

interface ChatInterfaceProps {
  agent: Agent;
}

export const ChatInterface = ({ agent }: ChatInterfaceProps) => {
  const room = useRoomContext();
  const params = useParams();
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const [inputValue, setInputValue] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isMessageLoading, setIsMessageLoading] = useState(false);
  const [workflowStage, setWorkflowStage] = useState<WorkflowStatus | null>(
    null
  );
  const [steps, setSteps] = useState<WorkflowStep[]>([]);
  const [workflowResult, setWorkflowResult] =
    useState<LivekitWorkflowUpdateMessageType | null>(null);
  const [inputDisabled, setInputDisabled] = useState(false);
  const [isInitialResponseError, setIsInitialResponseError] = useState(false);
  const [isApprovalRequired, setIsApprovalRequired] = useState(false);

  const { user } = useUserStore();

  const {
    data: messagesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery<MessageList>({
    queryKey: ["messages", params.chatId],
    queryFn: async ({ pageParam = 1 }) => {
      return await communicationApi.getMessages({
        conversationId: params.chatId as string,
        page: pageParam as number,
        limit: 20,
      });
    },
    getNextPageParam: (lastPage: MessageList) => {
      if (lastPage.metadata.hasNextPage) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    enabled: !!params.chatId,
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const employeeName = agent.name ?? "Unknown Employee";
  const employeeDesignation = agent.agent_topic_type ?? "AI Agent";
  const employeeDescription = agent.description ?? "AI Assistant";
  const userAvatar = user?.profileImage;
  const agentAvatar = agent?.avatar;

  // Add smooth scroll to bottom function
  const scrollToBottom = (behavior: ScrollBehavior = "smooth") => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  // Update messages state when new messages are fetched
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  useEffect(() => {
    if (!messagesData) return;

    if (isInitialLoad) {
      // On initial load, replace all messages
      const allMessages = messagesData.pages.flatMap((page) =>
        page.data
          .filter((msg) => msg.workflowResponse === null)
          .map((msg) => ({
            id: msg.id,
            content: msg.content || "",
            workflowId: msg.workflowId || null,
            workflowResponse: null,
            senderType:
              msg.senderType === "SENDER_TYPE_USER"
                ? SenderType.USER
                : SenderType.ASSISTANT,
          }))
      );

      // Sort messages by creation time (newest last)
      const sortedMessages = allMessages.reverse();
      setMessages(sortedMessages);
      setIsInitialLoad(false);

      // Scroll to bottom on initial load with instant behavior
      scrollToBottom("instant");
    }
  }, [messagesData, isInitialLoad]);

  // Register handlers for both transcription and workflow updates
  useEffect(() => {
    if (!room) return;

    // Handler for normal llm messages
    room.registerTextStreamHandler(
      LivekitRoomTopics.NORMAL_OUTPUT_MESSAGE,
      async (reader) => {
        const messageId = crypto.randomUUID();
        let accumulatedJsonString = "";
        let messageState: "pending" | "added" | "initial_handled" = "pending";
        let lastDisplayedContentLength = 0;

        try {
          for await (const chunk of reader) {
            if (messageState === "initial_handled") {
              // If already determined to be an initial response, consume the rest without processing
              accumulatedJsonString += chunk;
              continue;
            }

            accumulatedJsonString += chunk;

            try {
              // Try to parse the accumulated JSON
              const parsedOutput = JSON.parse(
                accumulatedJsonString
              ) as LivekitNormalOutputMessageType;

              // Successfully parsed the accumulated string
              if (parsedOutput.is_initial_response) {
                setIsLoading(false); // Handles global agent readiness
                if (messageState === "added") {
                  // Remove the message if it was being displayed but is actually initial response
                  setMessages((prev) =>
                    prev.filter((msg) => msg.id !== messageId)
                  );
                }
                messageState = "initial_handled";
                setIsMessageLoading(false);
              } else {
                // Not an initial response, extract and display only the content
                const messageText = parsedOutput.content ?? "";

                // Only update if we have new content to show
                if (messageText.length > lastDisplayedContentLength) {
                  if (messageState === "pending") {
                    // First time we have valid content, add the message
                    setMessages((prev) => [
                      ...prev,
                      {
                        id: messageId,
                        content: messageText,
                        workflowId: null,
                        workflowResponse: null,
                        senderType: SenderType.ASSISTANT,
                      },
                    ]);
                    messageState = "added";
                    setIsMessageLoading(false);
                  } else if (messageState === "added") {
                    // Update existing message with new content
                    setMessages((prevMessages) =>
                      prevMessages.map((msg) =>
                        msg.id === messageId
                          ? { ...msg, content: messageText }
                          : msg
                      )
                    );
                  }
                  lastDisplayedContentLength = messageText.length;
                  scrollToBottom();
                }
              }
            } catch (e) {
              // JSON parse error (incomplete JSON) - continue accumulating
              // This is expected during streaming until we have complete JSON
            }
          }

          // Handle final state after stream ends
          if (messageState === "pending" && accumulatedJsonString) {
            try {
              const finalParsedOutput = JSON.parse(
                accumulatedJsonString
              ) as LivekitNormalOutputMessageType;

              if (finalParsedOutput.is_initial_response) {
                setIsLoading(false);
              } else if (typeof finalParsedOutput.content === "string") {
                setMessages((prev) => [
                  ...prev,
                  {
                    id: messageId,
                    content: finalParsedOutput.content,
                    workflowId: null,
                    workflowResponse: null,
                    senderType: SenderType.ASSISTANT,
                  },
                ]);
                setIsMessageLoading(false);
                scrollToBottom();
              }
            } catch (finalError) {
              console.error("Error parsing final JSON:", finalError);
              setIsMessageLoading(false);
            }
          } else if (messageState === "pending") {
            setIsMessageLoading(false);
          }
        } catch (streamReadError) {
          console.error("Error reading transcription stream:", streamReadError);
          if (messageState === "added") {
            setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
          }
          setIsMessageLoading(false);
        } finally {
          setInputDisabled(false);
        }
      }
    );

    // Handler for workflow updates
    room.registerTextStreamHandler(
      LivekitRoomTopics.WORKFLOW_UPDATES,
      async (reader, participantInfo) => {
        try {
          const text = await reader.readAll();
          console.log(
            `Received workflow update from ${participantInfo.identity}: ${text}`
          );
          const result: LivekitWorkflowUpdateMessageType = JSON.parse(text);

          if (result.status === StepsStatus.CONNECTING) {
            if (steps.length === 0) {
              setIsMessageLoading(false);
            }
            setSteps((prev) => [
              ...prev,
              { step: result.tool_name, status: result.status },
            ]);
          }

          if (result.status === StepsStatus.COMPLETED) {
            setSteps((prev) =>
              prev.map((step) =>
                step.step === result.tool_name
                  ? { ...step, status: StepsStatus.COMPLETED }
                  : step
              )
            );
            // Store the workflow result when a step is completed
            setWorkflowResult(result);
          }

          if (result.status === StepsStatus.TIME_LOGGED) {
            setSteps((prev) =>
              prev.map((step) =>
                step.step === result.tool_name
                  ? { ...step, timeLogged: String(result.result) }
                  : step
              )
            );
          }

          if (
            result.status === StepsStatus.FAILED ||
            result.status === StepsStatus.ERROR ||
            result.status === StepsStatus.DISCONNECTED
          ) {
            setSteps((prev) =>
              prev.map((step) =>
                step.step === result.tool_name
                  ? { ...step, status: result.status }
                  : step
              )
            );
            setWorkflowResult(null);
            setSteps([]);
          }

          if (result.workflow_status === WorkflowStatus.RUNNING) {
            setWorkflowStage(WorkflowStatus.RUNNING);
          }

          if (
            result.workflow_status === WorkflowStatus.WAITING_FOR_APPROVAL &&
            result.status === StepsStatus.COMPLETED
          ) {
            setWorkflowStage(WorkflowStatus.WAITING_FOR_APPROVAL);
            setMessages((prev) => [
              ...prev,
              {
                id: crypto.randomUUID(),
                content: "",
                workflowId: null,
                workflowResponse: result,
                senderType: SenderType.ASSISTANT,
                isApprovalRequired: true,
              },
            ]);
          }

          if (result.workflow_status === WorkflowStatus.COMPLETED) {
            setInputDisabled(false);
            setWorkflowStage(WorkflowStatus.COMPLETED);
            setMessages((prev) => [
              ...prev,
              {
                id: crypto.randomUUID(),
                content: "",
                workflowId: null,
                workflowResponse: workflowResult,
                senderType: SenderType.ASSISTANT,
              },
            ]);
          }

          if (
            result.workflow_status === WorkflowStatus.CANCELLED &&
            result.status === StepsStatus.CANCELLED
          ) {
            setInputDisabled(false);
            setWorkflowStage(WorkflowStatus.CANCELLED);
            setMessages((prev) => [
              ...prev,
              {
                id: crypto.randomUUID(),
                content: "Workflow Cancelled",
                workflowId: null,
                workflowResponse: result,
                senderType: SenderType.ASSISTANT,
                isError: true,
              },
            ]);
          }

          if (
            result.workflow_status === WorkflowStatus.FAILED &&
            result.status === StepsStatus.FAILED
          ) {
            setInputDisabled(false);
            setWorkflowStage(WorkflowStatus.FAILED);
            setMessages((prev) => [
              ...prev,
              {
                id: crypto.randomUUID(),
                content: "Workflow Failed, Please try again",
                workflowId: null,
                workflowResponse: result,
                senderType: SenderType.ASSISTANT,
                isError: true,
              },
            ]);
          }
        } catch (error) {
          console.error("Error processing workflow update:", error);
        } finally {
          setIsMessageLoading(false);
        }
      }
    );

    // Cleanup function to unregister both handlers
    return () => {
      room.unregisterTextStreamHandler(LivekitRoomTopics.NORMAL_OUTPUT_MESSAGE);
      room.unregisterTextStreamHandler(LivekitRoomTopics.WORKFLOW_UPDATES);
    };
  }, [room, steps]); // Added steps to dependency array as it's modified in the handler

  const handleSendClick = async () => {
    const text = inputValue.trim();
    if (!text || !room) return;

    const messagePayload: LivekitInputMessageType = {
      content: text,
      workflow: null,
    };

    // Optimistically add user message
    setMessages((prev) => [
      ...prev,
      {
        id: crypto.randomUUID(),
        content: messagePayload,
        workflowId: null,
        workflowResponse: null,
        senderType: SenderType.USER,
      },
    ]);

    try {
      await room.localParticipant.sendText(JSON.stringify(messagePayload), {
        topic: LivekitRoomTopics.INPUT,
      });
      setWorkflowStage(null);
      setSteps([]);
      setWorkflowResult(null);
      setInputValue("");
      setInputDisabled(true);
      setIsMessageLoading(true);
    } catch (err) {
      console.error("Failed to send chat message:", err);
      setMessages((prev) => prev.slice(0, -1)); // Remove optimistically added message
    }
  };

  // this is from the form
  const handleStartWorkflow = async (
    workflowPayload: WorkflowPayload,
    workflowName: string
  ) => {
    if (!room) {
      return;
    }

    const workFlowStartMessage = `Execute ${sanitizeString(
      workflowName
    )} workflow with the following payload.`;

    workflowPayload.approval = isApprovalRequired;

    const messagePayload: LivekitInputMessageType = {
      content: workFlowStartMessage,
      workflow: workflowPayload,
    };

    setMessages((prev) => [
      ...prev,
      {
        id: crypto.randomUUID(),
        content: messagePayload,
        workflowId: null,
        workflowResponse: null,
        senderType: SenderType.USER,
      },
    ]);

    // Reset the workflow stage and steps
    setIsMessageLoading(true);
    setWorkflowStage(null); // Reset workflow stage for a new run
    setSteps([]); // Reset steps for a new run
    setWorkflowResult(null); // Reset previous results

    try {
      console.log("Sending workflow start message to LiveKit:", messagePayload);
      await room.localParticipant.sendText(JSON.stringify(messagePayload), {
        topic: LivekitRoomTopics.INPUT,
      });
      setInputDisabled(true);
    } catch (err) {
      console.error("Failed to send workflow start message:", err);
      setMessages((prev) => prev.slice(0, -1)); // Remove optimistically added message
      setIsMessageLoading(false);
    }
  };

  const handleApproval = async (decision: ApprovalDecision) => {
    if (!room) return;

    const messagePayload = {
      decision: decision,
    };

    console.log("messagePayload", messagePayload);

    try {
      await room.localParticipant.sendText(JSON.stringify(messagePayload), {
        topic: LivekitRoomTopics.WORKFLOW_APPROVAL_INPUT,
      });
      if (decision === ApprovalDecision.REJECT) {
        setWorkflowResult(null);
        setSteps([]);
        setWorkflowStage(null);
      }
    } catch (err) {
      toast.error("Failed to send approval decision");
      console.error("Failed to send approval:", err);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 items-center justify-center h-full">
        <LoaderIcon className="w-10 h-10 animate-spin text-brand-primary" />
        <h1 className="text-xl font-medium text-brand-primary-font">
          Please wait while your agent is getting ready...
        </h1>
      </div>
    );
  }

  return (
    <>
      <div
        className="flex flex-col flex-1 gap-4 pt-8 w-full overflow-y-auto px-6 pb-28"
        ref={chatContainerRef}
      >
        {messages.length === 0 &&
        !isMessageLoading &&
        workflowStage === null &&
        !isInitialResponseError ? (
          <EmployeeChatStarter
            employeeAvatar={agent.avatar}
            employeeName={employeeName}
            employeeDescription={employeeDescription}
            employeeWorkflows={agent.workflow_ids ?? []}
          />
        ) : (
          <div className="w-full space-y-6 flex flex-col">
            {messages.map((message) => {
              if (message.senderType === SenderType.USER) {
                return (
                  <ChatBubble
                    key={message.id}
                    sender={message.senderType}
                    avatar={userAvatar ?? undefined}
                    name={employeeName}
                  >
                    <InputChatBubble
                      message={message.content as LivekitInputMessageType}
                    />
                  </ChatBubble>
                );
              } else {
                // Assistant Message
                const msg = message as ChatMessage;
                const ownStatus = msg.workflowResponse?.workflow_status;
                const ownResponse = msg.workflowResponse;

                const isTerminalStatus =
                  ownStatus &&
                  [
                    WorkflowStatus.COMPLETED,
                    WorkflowStatus.FAILED,
                    WorkflowStatus.CANCELLED,
                  ].includes(ownStatus);

                const isSimpleText =
                  !ownResponse &&
                  typeof msg.content === "string" &&
                  msg.content.length > 0;

                let displayWorkflowStage = workflowStage;
                let displaySteps = steps;
                let displayWorkflowResult = workflowResult;

                if (isTerminalStatus) {
                  displayWorkflowStage = ownStatus;
                  displayWorkflowResult = ownResponse;
                  // If this message's response is the *current* global workflowResult,
                  // then the global `steps` are relevant to it.
                  // Otherwise, global state has moved on, so don't show current global steps for this old message.
                  if (workflowResult !== ownResponse) {
                    displaySteps = [];
                  }
                } else if (isSimpleText) {
                  displayWorkflowStage = null;
                  displaySteps = [];
                  displayWorkflowResult = null;
                } else if (ownStatus === WorkflowStatus.WAITING_FOR_APPROVAL) {
                  displayWorkflowStage = ownStatus; // Show its own approval stage
                  displayWorkflowResult = ownResponse;
                  // If global stage is RUNNING (for a new workflow) and not for this approval,
                  // don't show global steps on this approval message.
                  if (
                    workflowStage === WorkflowStatus.RUNNING &&
                    workflowResult !== ownResponse
                  ) {
                    displaySteps = [];
                  }
                }

                return (
                  <ChatBubble
                    key={message.id}
                    sender={message.senderType}
                    avatar={agentAvatar}
                    name={employeeName}
                  >
                    <OutputChatBubble
                      message={msg}
                      workflowStage={displayWorkflowStage}
                      workflowResult={displayWorkflowResult}
                      steps={displaySteps}
                      handleApproval={handleApproval}
                    />
                  </ChatBubble>
                );
              }
            })}

            {/* Show ThinkingBubble when workflow is running */}
            {workflowStage === WorkflowStatus.RUNNING && (
              <ChatBubble
                sender={SenderType.ASSISTANT}
                avatar={agentAvatar}
                name={employeeName}
              >
                <OutputChatBubble
                  message={{
                    id: crypto.randomUUID(),
                    content: "",
                    workflowId: null,
                    workflowResponse: null,
                    senderType: SenderType.ASSISTANT,
                  }}
                  workflowStage={workflowStage}
                  workflowResult={workflowResult}
                  steps={steps}
                  handleApproval={handleApproval}
                />
              </ChatBubble>
            )}

            {isMessageLoading &&
              !workflowStage && ( // Only show generic loading if not in an active workflow stage
                <ChatBubble
                  sender={SenderType.ASSISTANT}
                  avatar={agentAvatar}
                  name={employeeName}
                >
                  <LoadingBubble />
                </ChatBubble>
              )}
          </div>
        )}
      </div>
      <ChatInput
        inputValue={inputValue}
        onInputChange={handleInputChange}
        onSendClick={handleSendClick}
        employeeName={employeeName}
        employeeDesignation={employeeDesignation}
        employeeWorkflows={agent.workflow_ids ?? null}
        disabled={inputDisabled}
        setIsApprovalRequired={setIsApprovalRequired}
        handleApproval={handleApproval}
      />
      <WorkflowStartingForm onStartWorkflow={handleStartWorkflow} />
    </>
  );
};

const LoadingBubble = () => {
  return (
    <div className="flex items-center justify-center py-2">
      <div className="flex gap-2">
        <div
          className="w-2 h-2 rounded-full bg-brand-primary animate-bounce"
          style={{ animationDelay: "0ms" }}
        />
        <div
          className="w-2 h-2 rounded-full bg-brand-primary animate-bounce"
          style={{ animationDelay: "150ms" }}
        />
        <div
          className="w-2 h-2 rounded-full bg-brand-primary animate-bounce"
          style={{ animationDelay: "300ms" }}
        />
      </div>
    </div>
  );
};
